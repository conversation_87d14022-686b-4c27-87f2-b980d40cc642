person = {"name":"su<PERSON><PERSON>", "age":"47", "city": "hyderabd"}

for key in person:
    print(key)

for value in person.values():
    print(value)    
    
for key,value in person.items():
    print(key,value)
    
for key,value in person.items():
    print(f"{key} -> {value}")
    
family={
    "mom":{"name":"A", "age":70},
    "dad":{"name":"B", "age":75}
}
#for name, infromation in family.items():
    #print(f"Parent Type:{name} \n")
    #parent_name = infromation["name"]
    #print(parent_name)


for parentType, infromation in family.items():
    print(f"\nParent Type:{parentType}")
    parent_name = f"Name: {infromation["name"]}"
    parent_age = f"age: {infromation["age"]}"
    print(f"\n {parent_name}")
    print(f"\n {parent_age}")


speakers = {
    "Alice Johnson": ["Python Best Practices", "Machine Learning Intro"],
    "<PERSON>": ["Cybersecurity Fundamentals", "Blockchain 101"],
    "Carol Lee": ["Data Visualization Techniques", "Advanced SQL Queries"],
}

#here above exampele : "Alice Johnson": ["Python Best Practices", "Machine Learning Intro"]
# "<PERSON>" will be speaker 
# and ["Python Best Practices", "Machine Learning Intro"] will be topics
for speaker, topics in speakers.items():
    print(f"{speaker} will talk:")
    for topic in topics:
        print(f"-{topic}")
