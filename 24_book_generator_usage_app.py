
#import book_generator

#book  = book_generator.Book(title="Ho<PERSON>Porter", author="sudhakar", genre="bookMovies")
#ebook  = book_generator.EBook(title="Mr.Bean", author="ebookSudhakar", genre="ebookMovies", format_="pdf", filesize="20")

from book_generator import EBook, Book
#from book_generator import *
book  = Book(title="HorryPorter", author="sudhakar", genre="bookMovies")
ebook = EBook(title="Mr.Bean", author="ebookSudhakar", genre="ebookMovies", format_="pdf", filesize="20")


book.read_book()

print(ebook.test())
print(book.describe_book())
print(ebook.download_book())
print(ebook.describe_book())
print(ebook.read_book())
