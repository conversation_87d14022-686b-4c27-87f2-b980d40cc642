class Book:
    def __init__(self, title, author, genre):
        self.title = title
        self.author = author
        self.genre = genre
        self.num_pages = 234
    
    def read_book(self):
        return f" {self.title} by Author:{self.author}"
    
    def describe_book(self):
        return f" {self.title} by Author:{self.author}, Genre:{self.genre}"
    
    def get_num_pages(self):
        print(f"The {self.title} has {self.num_pages} pages")
    
    def update_num_pages(self, new_pages):
        self.num_pages = new_pages
        print(f"{self.title} updated no of pages to {self.num_pages}")
        
    def __str__(self):
        return f" Book object: Title:{self.title}, Author:{self.author}, Genre:{self.genre}"

book = Book("The Hitchiker's Guide to the Galaxy", "Douglas Adams", "fiction")
# to update value directly from class
#book.num_pages = 1234

print(book.get_num_pages())
book.update_num_pages(new_pages=12)

#print(book.read_book())       # This will return and print the string
#print(book.describe_book())   # This will return and print the string