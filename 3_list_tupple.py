even_nums = list(range(0,100,2))
print(f"even_nums: {even_nums}")


list_sqrs = []
for value in range(1,11):
    list_sqrs.append(value)
print(list_sqrs)

list_sqrs2 = [value for value in range(1,11)]
print(list_sqrs2)

#tupple : but is it immutable
#list : mutable

name_tupple = ("abc","bcd") 
# not possible alter the data as it is imuutable 
# #and can't call the append remove pop mehtods on it

# tupple - immutable list
for name in name_tupple:
    print(name)

#name_tupple[0]="efgh";


