activities = ["hiking", "swimming", "museum", "picnic"]
group_interests = ["art", "history", "swimming"]

# check if an interest align with group interests
if "museum" in activities and ("art" in group_interests or
                               "history" in group_interests):
    print("We should visit the museum!")
elif "swimming" in activities and "swimming" in group_interests:
    print("Look like a day at the pool is in order!")
    