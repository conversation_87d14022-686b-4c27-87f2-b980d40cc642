class Book:
    def __init__(self, title, author, genre):
        self.title = title
        self.author = author
        self.genre = genre
    
    def read_book(self):
        return f" {self.title} by Author:{self.author}"
    
    def describe_book(self):
        return f" {self.title} by Author:{self.author}, Genre:{self.genre}"
        
    def __str__(self):
        return f" Book object: Title:{self.title}, Author:{self.author}, Genre:{self.genre}"

class EBook(Book):
    def __init__(self, title, author, genre, filesize,format_):
        super().__init__(title, author, genre)
        self.filesize = filesize
        self.format = format_
    
    def download_book(self):
        print(f"Downloadinng {self.title} in format {self.format} of size {self.filesize} MB")
        
    def test(self):
        print(f"author is :{self.author}")

#ebook  =EBook(title="abc", author="sudhakar", genre="movies")
book  =Book(title="abc", author="sudhakar", genre="movies")
ebook  =EBook(title="Mr.Bean", author="sudhakar", genre="movies", format_="pdf", filesize="20")
#print(ebook.test())
print(book.describe_book())
print(ebook.download_book())