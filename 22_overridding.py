class Book:
    def __init__(self, title, author, genre):
        self.title = title
        self.author = author
        self.genre = genre
    def read_book(self):
        #return f" {self.title} by Author:{self.author}"
        print(f"Reading (a physical book) {self.title} by Author:{self.author}")
    def describe_book(self):
        return f" {self.title} by Author:{self.author}, Genre:{self.genre}"
    def __str__(self):
        return f" Book object: Title:{self.title}, Author:{self.author}, Genre:{self.genre}"

class EBook(Book):
    def __init__(self, title, author, genre, filesize,format_):
        super().__init__(title, author, genre)
        self.filesize = filesize
        self.format = format_  
    def describe_book(self):
        #return super().describe_book()
        super().describe_book()
        print(f"File Size: {self.filesize}MB, Format: {self.format}")  
    def read_book(self):
        #return f" {self.title} by Author:{self.author}"
        print(f"Reading {self.title} on e-reader device...")
    def download_book(self):
        print(f"Downloadinng {self.title} in format {self.format} of size {self.filesize} MB")
    def test(self):
        print(f"author is :{self.author}")
#ebook  =EBook(title="abc", author="sudhakar", genre="movies")
book  = Book(title="HorryPorter", author="sudhakar", genre="bookMovies")
book.read_book()
ebook  = EBook(title="Mr.Bean", author="ebookSudhakar", genre="ebookMovies", format_="pdf", filesize="20")
#print(ebook.test())
print(book.describe_book())
#print(ebook.download_book())
print(ebook.describe_book())
print(ebook.read_book())