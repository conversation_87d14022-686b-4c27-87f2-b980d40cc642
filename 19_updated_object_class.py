class Book:
    def __init__(self, title, author, genre):
        self.title = title
        self.author = author
        self.genre = genre
    
    def read_book(self):
        return f" {self.title} by Author:{self.author}"
    
    def describe_book(self):
        return f" {self.title} by Author:{self.author}, Genre:{self.genre}"
        
    def __str__(self):
        return f" Book object: Title:{self.title}, Author:{self.author}, Genre:{self.genre}"

book = Book("The Hitchiker's Guide to the Galaxy", "Douglas Adams", "fiction")

print(book.read_book())       # This will return and print the string
print(book.describe_book())   # This will return and print the string