print("hello teja")
print(3-1+2+5)
name = "sudhakar"
message = 'hello there'

upper_case = message.upper();
print(upper_case)
print(name.title())
print(upper_case.title())

message2 = " hello how are you ? "
print(message2.strip())

message3 = f"hello, {name} how are you?"
print(message3)
print(message3.title()) #for capatilizing the first letter

# is used to comment a line

age = 100 # here 100 is integer
total = age / 45 # value would be float value
print(total)

fruits = ["apple","banana","cherry"]
fruits.append("mango")
fruits.reverse()
print(fruits)
message = f"Hello, my name is su<PERSON><PERSON> i would like a good {fruits[3]}"
print(message)

ages=[1,2,78,98,34,67]
print(len(ages))
print(f"Before Sorting: {ages}")
ages.sort()
print(f"After Sorting: {ages}")

for fruit in fruits:
    print(f"I like: {fruit},")
    
